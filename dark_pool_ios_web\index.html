<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Dark Pool">
    <meta name="theme-color" content="#0D47A1">
    
    <title>Dark Pool - iOS Training Assistant</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="icon-180.png">
    <link rel="apple-touch-icon" sizes="152x152" href="icon-152.png">
    <link rel="apple-touch-icon" sizes="120x120" href="icon-120.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0D1B2A, #1B263B, #415A77);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
            -webkit-user-select: none;
            user-select: none;
        }
        
        .container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
        }
        
        .header {
            text-align: center;
            padding: 40px 20px;
            animation: fadeInUp 1s ease-out;
        }
        
        .app-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #0D47A1, #1976D2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 20px 40px rgba(13, 71, 161, 0.3);
            animation: pulse 2s infinite;
        }
        
        .app-icon::before {
            content: "🎮";
            font-size: 60px;
        }
        
        .app-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            letter-spacing: 2px;
        }
        
        .app-subtitle {
            font-size: 16px;
            opacity: 0.8;
            letter-spacing: 1px;
        }
        
        .main-content {
            flex: 1;
            padding: 0 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            animation: slideInLeft 0.8s ease-out;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
        }
        
        .feature-title {
            font-size: 16px;
            font-weight: bold;
        }
        
        .feature-description {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.4;
        }
        
        .launch-button {
            background: linear-gradient(135deg, #0D47A1, #1976D2);
            border: none;
            border-radius: 30px;
            padding: 20px;
            margin: 40px 0;
            color: white;
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 1px;
            box-shadow: 0 15px 30px rgba(13, 71, 161, 0.4);
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }
        
        .launch-button:active {
            transform: scale(0.95);
        }
        
        .launch-button::before {
            content: "▶";
            font-size: 24px;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 14px;
            opacity: 0.6;
        }
        
        .install-prompt {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin: 20px;
            text-align: center;
            display: none;
        }
        
        .install-button {
            background: #4CAF50;
            border: none;
            border-radius: 20px;
            padding: 10px 20px;
            color: white;
            font-weight: bold;
            margin-top: 10px;
            cursor: pointer;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
        
        /* iOS specific styles */
        @supports (-webkit-touch-callout: none) {
            .container {
                padding-top: max(env(safe-area-inset-top), 20px);
                padding-bottom: max(env(safe-area-inset-bottom), 20px);
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #000000, #1a1a1a, #2d2d2d);
            }
        }
        
        /* Responsive design */
        @media (max-width: 375px) {
            .app-title {
                font-size: 28px;
            }
            
            .feature-card {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="app-icon"></div>
            <h1 class="app-title">Dark Pool</h1>
            <p class="app-subtitle">iOS Training Assistant</p>
        </div>
        
        <div class="install-prompt" id="installPrompt">
            <p>📱 Add Dark Pool to your home screen for the best experience!</p>
            <button class="install-button" onclick="showInstallInstructions()">Install App</button>
        </div>
        
        <div class="main-content">
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: rgba(76, 175, 80, 0.2);">🧠</div>
                    <div class="feature-title">AI-Powered Analysis</div>
                </div>
                <div class="feature-description">Advanced computer vision optimized for iOS Safari</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: rgba(255, 152, 0, 0.2);">⏱️</div>
                    <div class="feature-title">Real-time Assistance</div>
                </div>
                <div class="feature-description">Live trajectory prediction with iOS performance</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: rgba(156, 39, 176, 0.2);">📊</div>
                    <div class="feature-title">Training Statistics</div>
                </div>
                <div class="feature-description">Comprehensive analytics with iOS integration</div>
            </div>
            
            <button class="launch-button" onclick="launchTraining()">
                Start Training
            </button>
        </div>
        
        <div class="footer">
            <p>Developed by Hazem Farouk</p>
            <p style="font-size: 12px; margin-top: 5px;">For Educational Purposes Only</p>
        </div>
    </div>
    
    <script>
        // PWA Installation
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            document.getElementById('installPrompt').style.display = 'block';
        });
        
        function showInstallInstructions() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                });
            } else {
                // Show iOS install instructions
                alert('To install:\n1. Tap the Share button\n2. Tap "Add to Home Screen"\n3. Tap "Add"');
            }
        }
        
        function launchTraining() {
            // iOS-style alert
            if (confirm('Training mode activated!\n\nConnect to 8 Ball Pool to begin.')) {
                // Add haptic feedback for iOS
                if (navigator.vibrate) {
                    navigator.vibrate(100);
                }
                
                // Add visual feedback
                const button = event.target;
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = 'scale(1)';
                }, 150);
            }
        }
        
        // iOS-specific optimizations
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent zoom on input focus
            document.addEventListener('touchstart', function() {}, true);
            
            // Add iOS momentum scrolling
            document.body.style.webkitOverflowScrolling = 'touch';
            
            // Hide address bar on iOS
            window.addEventListener('load', function() {
                setTimeout(function() {
                    window.scrollTo(0, 1);
                }, 0);
            });
        });
        
        // Service Worker for PWA
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('sw.js')
                .then(registration => console.log('SW registered'))
                .catch(error => console.log('SW registration failed'));
        }
    </script>
</body>
</html>
